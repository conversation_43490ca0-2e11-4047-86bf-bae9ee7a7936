// ==UserScript==
// @name         AI Studio 多功能脚本合集（更新版）
// @namespace    http://tampermonkey.net/
// @version      1.5.0
// @description  此脚本整合了三个主要功能：项目创建、API KEY 自动生成、API KEY 提取。生成/提取完 API KEY 后自动复制到剪贴板；若失败则弹出 textarea 兜底，手机浏览器也可一键长按复制。
// <AUTHOR>
// @match        *://*.console.cloud.google.com/*
// @match        *://*.aistudio.google.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_setClipboard
// @run-at       document-idle
// ==/UserScript==

(function() {
    'use strict';

    // {{ AURA-X: Add - 添加全局配置常量，提高可维护性 }}
    /**
     * 全局配置常量
     * 集中管理所有超时时间、延迟、重试次数等配置参数
     */
    const CONFIG = {
        // 通用配置
        TIMEOUTS: {
            ELEMENT_WAIT: 15000,        // 元素等待超时时间
            ELEMENTS_WAIT: 20000,       // 多元素等待超时时间
            DIALOG_WAIT: 25000,         // 对话框等待超时时间
            SHORT_WAIT: 5000            // 短等待超时时间
        },
        DELAYS: {
            STANDARD: 300,              // 标准延迟
            SHORT: 250,                 // 短延迟
            MEDIUM: 500,                // 中等延迟
            LONG: 1000,                 // 长延迟
            EXTRA_LONG: 2000,           // 超长延迟
            BETWEEN_PROJECTS: 5000      // 项目创建间隔
        },
        RETRY: {
            MAX_REFRESH: 5,             // 最大刷新次数
            TARGET_PROJECTS: 12         // 目标项目数量
        },
        STORAGE_KEYS: {
            REFRESH_COUNT: 'aiStudioAutoRefreshCountSilentColorOpt',
            BUTTON_POSITION: 'aiStudioFloatingButtonPosition'
        },
        // {{ AURA-X: Add - 添加拖拽功能相关配置 }}
        DRAG: {
            ANIMATION_DURATION: 150,        // 动画持续时间（毫秒）- 减少延迟
            BOUNDARY_PADDING: 10,           // 边界内边距（像素）
            DRAG_THRESHOLD: 3,              // 拖拽阈值（像素）- 降低阈值提高响应
            DEFAULT_POSITION: {             // 默认位置
                top: 10,
                right: 10
            },
            // {{ AURA-X: Add - 添加性能优化配置 }}
            THROTTLE_DELAY: 8               // 节流延迟（毫秒）- 约120fps
        }
    };

    // {{ AURA-X: Add - 添加CSS选择器常量，便于维护和修改 }}
    /**
     * CSS选择器常量
     * 集中管理所有页面元素选择器，便于维护
     */
    const SELECTORS = {
        // Google Cloud Console 选择器
        CLOUD: {
            PROJECT_SWITCHER: 'button.mdc-button.mat-mdc-button span.cfc-switcher-button-label-text',
            CREATE_PROJECT_BTN: 'button.purview-picker-create-project-button',
            MANAGE_QUOTAS_BTN: 'a[track-name="create-project-quota-warning-request-increase"]',
            SUBMIT_BTN: 'button.projtest-create-form-submit',
            QUOTA_SUBMIT: 'a#p6ntest-quota-submit-button',
            DIALOG_CONTENT: 'mat-dialog-content p, mat-dialog-content div, mat-dialog-container p, mat-dialog-container div'
        },
        // AI Studio 选择器
        AISTUDIO: {
            CREATE_API_BTN: 'button[iconname="add"].ms-button-primary',
            DIALOG_CONTENT: 'mat-dialog-content',
            PROJECT_INPUT: 'input[aria-label="Search Google Cloud projects"]',
            PROJECT_OPTION: 'mat-option.mat-mdc-option',
            PROJECT_NAME: 'span.v3-font-body',
            CREATE_IN_PROJECT_BTN: 'mat-dialog-content button.create-api-key-button',
            API_KEY_DISPLAY: 'div.apikey-text',
            PROJECT_ROW: 'project-table div[role="rowgroup"].table-body > div[role="row"].table-row',
            PROJECT_NAME_CELL: 'div[role="cell"].project-cell > div:first-child',
            API_KEY_LINK: 'div[role="cell"].project-cell + div[role="cell"].key-cell a.apikey-link'
        },
        // 通用对话框选择器
        DIALOG: {
            CLOSE_BUTTONS: [
                'button[aria-label="Close dialog"]',
                'button[aria-label="关闭"]',
                'mat-dialog-actions button:nth-child(1)',
                'button.cancel-button',
                'button:contains("Cancel")',
                'button:contains("取消")',
                'button:contains("Done")',
                'button:contains("完成")',
                'button:contains("Close")',
                'mat-dialog-actions button:last-child'
            ]
        }
    };

    // {{ AURA-X: Modify - 重构公共工具函数，统一接口和错误处理 }}
    /**
     * 公共工具函数类
     * 提供统一的DOM操作、延迟、错误处理等功能
     */
    class Utils {
        /**
         * 延迟执行
         * @param {number} ms - 延迟毫秒数
         * @returns {Promise} Promise对象
         */
        static delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 等待单个元素出现
         * @param {string} selector - CSS选择器
         * @param {number} timeout - 超时时间（毫秒）
         * @param {Element} root - 根元素，默认为document
         * @param {boolean} checkDisabled - 是否检查元素是否被禁用
         * @returns {Promise<Element>} 找到的元素
         */
        static async waitForElement(selector, timeout = CONFIG.TIMEOUTS.ELEMENT_WAIT, root = document, checkDisabled = true) {
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                let element = null;
                try {
                    element = root.querySelector(selector);
                } catch (error) {
                    // 忽略选择器错误，继续尝试
                }

                if (element && element.offsetParent !== null) {
                    const computedStyle = getComputedStyle(element);
                    const isVisible = computedStyle.display !== 'none' &&
                                    computedStyle.visibility !== 'hidden' &&
                                    parseFloat(computedStyle.opacity) > 0;
                    const isEnabled = !checkDisabled || !element.disabled;

                    if (isVisible && isEnabled) {
                        return element;
                    }
                }

                await this.delay(CONFIG.DELAYS.SHORT);
            }

            throw new Error(`等待元素超时: "${selector}" (${timeout}ms)`);
        }

        /**
         * 等待多个元素出现
         * @param {string} selector - CSS选择器
         * @param {number} minCount - 最少元素数量
         * @param {number} timeout - 超时时间（毫秒）
         * @param {Element} root - 根元素，默认为document
         * @returns {Promise<NodeList>} 找到的元素列表
         */
        static async waitForElements(selector, minCount = 1, timeout = CONFIG.TIMEOUTS.ELEMENTS_WAIT, root = document) {
            const startTime = Date.now();

            while (Date.now() - startTime < timeout) {
                const elements = root.querySelectorAll(selector);
                if (elements.length >= minCount && elements[0].offsetParent !== null) {
                    return elements;
                }
                await this.delay(CONFIG.DELAYS.STANDARD);
            }

            throw new Error(`等待元素超时: 需要至少 ${minCount} 个 "${selector}" (${timeout}ms)`);
        }
    }

    // {{ AURA-X: Add - 添加对话框管理工具类，统一对话框操作 }}
    /**
     * 对话框管理工具类
     * 提供统一的对话框检测和关闭功能
     */
    class DialogManager {
        /**
         * 检查是否达到配额限制
         * @returns {Promise<boolean>} 是否达到限制
         */
        static async checkQuotaLimit() {
            try {
                // 检查配额提交按钮
                if (document.querySelector(SELECTORS.CLOUD.QUOTA_SUBMIT)) {
                    return true;
                }

                // 检查对话框中的配额限制文本
                const dialogTexts = document.querySelectorAll(SELECTORS.CLOUD.DIALOG_CONTENT);
                const quotaLimitPattern = /quota (limit|has been reached|creation limit)/i;

                return Array.from(dialogTexts).some(element =>
                    quotaLimitPattern.test(element.textContent)
                );
            } catch (error) {
                console.warn('检查配额限制时出错:', error);
                return false;
            }
        }

        /**
         * 尝试关闭对话框
         * @returns {Promise<void>}
         */
        static async closeDialog() {
            for (const selector of SELECTORS.DIALOG.CLOSE_BUTTONS) {
                let button = null;

                // 处理包含文本的选择器
                if (selector.includes(':contains')) {
                    const textMatch = selector.match(/:contains\(["']?([^"']+)/);
                    if (textMatch) {
                        const targetText = textMatch[1].toLowerCase();
                        const buttons = document.querySelectorAll('button');
                        button = Array.from(buttons).find(btn =>
                            btn.textContent.trim().toLowerCase() === targetText
                        );
                    }
                } else {
                    button = document.querySelector(selector);
                }

                if (button && button.offsetParent !== null) {
                    button.click();
                    await Utils.delay(CONFIG.DELAYS.LONG);
                    return;
                }
            }
        }
    }

    // {{ AURA-X: Modify - 重构项目创建流程，提高可读性和可维护性 }}
    /**
     * 项目创建管理类
     * 负责Google Cloud Console项目的自动创建
     */
    class ProjectCreator {
        constructor() {
            this.successCount = 0;
            this.refreshCount = parseInt(GM_getValue(CONFIG.STORAGE_KEYS.REFRESH_COUNT, '0'), 10);
        }

        /**
         * 检查当前页面是否为Google Cloud Console
         * @returns {boolean} 是否为正确页面
         */
        static isValidPage() {
            return /console\.cloud\.google\.com/.test(location.host);
        }

        /**
         * 重定向到Google Cloud Console
         */
        static redirectToConsole() {
            location.href = "https://console.cloud.google.com";
        }

        /**
         * 创建单个项目
         * @returns {Promise<Object>} 创建结果
         */
        async createSingleProject() {
            try {
                // 检查配额限制
                if (await DialogManager.checkQuotaLimit()) {
                    return { limitReached: true };
                }

                // 点击项目切换器
                const projectSwitcher = await Utils.waitForElement(SELECTORS.CLOUD.PROJECT_SWITCHER);
                projectSwitcher.click();
                await Utils.delay(CONFIG.DELAYS.EXTRA_LONG);

                // 再次检查配额限制
                if (await DialogManager.checkQuotaLimit()) {
                    await DialogManager.closeDialog();
                    return { limitReached: true };
                }

                // 点击创建项目按钮
                const createButton = await Utils.waitForElement(SELECTORS.CLOUD.CREATE_PROJECT_BTN);
                createButton.click();
                await Utils.delay(CONFIG.DELAYS.EXTRA_LONG + 500);

                // 检查配额限制
                if (await DialogManager.checkQuotaLimit()) {
                    await DialogManager.closeDialog();
                    return { limitReached: true };
                }

                // 等待配额信息提醒信息加载完毕
                await Utils.waitForElement(SELECTORS.CLOUD.MANAGE_QUOTAS_BTN);
                await Utils.delay(CONFIG.TIMEOUTS.STANDARD);

                // 提交项目创建表单
                const submitButton = await Utils.waitForElement(
                    SELECTORS.CLOUD.SUBMIT_BTN,
                    CONFIG.TIMEOUTS.ELEMENT_WAIT
                );
                submitButton.click();

                return { limitReached: false };

            } catch (error) {
                console.error('创建项目时出错:', error);
                await DialogManager.closeDialog();

                // 如果未达到最大刷新次数，则刷新页面重试
                if (this.refreshCount < CONFIG.RETRY.MAX_REFRESH) {
                    this.refreshCount++;
                    GM_setValue(CONFIG.STORAGE_KEYS.REFRESH_COUNT, this.refreshCount.toString());
                    location.reload();
                    return { refreshed: true };
                }

                throw error;
            }
        }

        /**
         * 执行项目创建流程
         * @param {Function} progressCallback - 进度回调函数
         * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
         */
        async run(progressCallback = null) {
            if (!ProjectCreator.isValidPage()) {
                ProjectCreator.redirectToConsole();
                return { current: 0, total: 0 };
            }

            console.log(`开始创建项目，目标数量: ${CONFIG.RETRY.TARGET_PROJECTS}`);

            // 初始化进度显示
            if (progressCallback) {
                progressCallback(0, CONFIG.RETRY.TARGET_PROJECTS, '初始化...');
            }

            for (let i = 1; i <= CONFIG.RETRY.TARGET_PROJECTS; i++) {
                console.log(`正在创建第 ${i} 个项目...`);

                // 更新进度显示
                if (progressCallback) {
                    progressCallback(i - 1, CONFIG.RETRY.TARGET_PROJECTS, `创建第${i}个项目...`);
                }

                const result = await this.createSingleProject();

                if (result.limitReached) {
                    console.log('已达到配额限制，停止创建');
                    if (progressCallback) {
                        progressCallback(this.successCount, CONFIG.RETRY.TARGET_PROJECTS, '已达配额限制');
                    }
                    break;
                }

                if (result.refreshed) {
                    console.log('页面已刷新，重新开始');
                    if (progressCallback) {
                        progressCallback(this.successCount, CONFIG.RETRY.TARGET_PROJECTS, '页面刷新中...');
                    }
                    return { current: this.successCount, total: CONFIG.RETRY.TARGET_PROJECTS };
                }

                this.successCount++;
                console.log(`第 ${i} 个项目创建成功`);

                // 更新成功进度
                if (progressCallback) {
                    progressCallback(this.successCount, CONFIG.RETRY.TARGET_PROJECTS, `已完成${this.successCount}个项目`);
                }

                // 如果不是最后一个项目，则等待一段时间
                if (i < CONFIG.RETRY.TARGET_PROJECTS) {
                    if (progressCallback) {
                        progressCallback(this.successCount, CONFIG.RETRY.TARGET_PROJECTS, '等待中...');
                    }
                    await Utils.delay(CONFIG.DELAYS.BETWEEN_PROJECTS);
                }
            }

            // 重置刷新计数器
            GM_setValue(CONFIG.STORAGE_KEYS.REFRESH_COUNT, '0');
            console.log(`项目创建完成，成功创建 ${this.successCount} 个项目`);

            return { current: this.successCount, total: CONFIG.RETRY.TARGET_PROJECTS };
        }
    }

    /**
     * 运行项目创建流程
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
     */
    async function runProjectCreation(progressCallback = null) {
        const creator = new ProjectCreator();
        return await creator.run(progressCallback);
    }

    // {{ AURA-X: Add - 添加剪贴板管理工具类，统一剪贴板操作 }}
    /**
     * 剪贴板管理工具类
     * 提供统一的剪贴板操作和备用方案
     */
    class ClipboardManager {
        /**
         * 复制文本到剪贴板，失败时显示文本框
         * @param {string} text - 要复制的文本
         * @param {string} successMessage - 成功提示信息
         * @param {string} fallbackMessage - 失败提示信息
         */
        static copyToClipboard(text, successMessage, fallbackMessage) {
            try {
                GM_setClipboard(text, 'text');
                alert(successMessage);
            } catch (error) {
                console.warn('剪贴板写入失败，使用备用方案:', error);
                this.showTextAreaFallback(text, fallbackMessage);
            }
        }

        /**
         * 显示文本框备用方案
         * @param {string} text - 要显示的文本
         * @param {string} message - 提示信息
         */
        static showTextAreaFallback(text, message) {
            const textarea = document.createElement('textarea');
            Object.assign(textarea.style, {
                position: 'fixed',
                top: '10%',
                left: '5%',
                width: '90%',
                height: '80%',
                zIndex: '10000',
                fontSize: '14px',
                padding: '10px',
                border: '2px solid #007cba',
                borderRadius: '4px',
                backgroundColor: '#fff'
            });

            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            textarea.focus();

            alert(message);
        }
    }

    // {{ AURA-X: Modify - 重构API KEY生成流程，提高代码组织性 }}
    /**
     * API密钥创建管理类
     * 负责AI Studio中API密钥的自动生成
     */
    class ApiKeyCreator {
        constructor() {
            this.projectSummary = {};
            this.allKeys = [];
        }

        /**
         * 检查当前页面是否为AI Studio API密钥页面
         * @returns {boolean} 是否为正确页面
         */
        static isValidPage() {
            return /aistudio\.google\.com\/apikey/.test(location.href);
        }

        /**
         * 重定向到AI Studio API密钥页面
         */
        static redirectToApiKeyPage() {
            location.href = "https://aistudio.google.com/apikey";
        }

        /**
         * 获取现有项目名称列表
         * @returns {Set<string>} 现有项目名称集合
         */
        getExistingProjectNames() {
            const existingNames = new Set();

            try {
                const projectRows = document.querySelectorAll(SELECTORS.AISTUDIO.PROJECT_ROW);

                for (const row of projectRows) {
                    const nameElement = row.querySelector(SELECTORS.AISTUDIO.PROJECT_NAME_CELL);
                    if (nameElement?.textContent) {
                        existingNames.add(nameElement.textContent.trim());
                    }
                }

                console.log(`已获取到 ${existingNames.size} 个现有项目名称:`, Array.from(existingNames));
            } catch (error) {
                console.warn('获取现有项目列表失败，将继续处理所有项目:', error);
            }

            return existingNames;
        }

        /**
         * 从创建密钥弹窗中获取项目列表
         * @returns {Promise<Array>} 项目信息数组
         */
        async getAvailableProjects() {
            try {
                // 点击创建API密钥按钮
                const createButton = await Utils.waitForElement(SELECTORS.AISTUDIO.CREATE_API_BTN);
                createButton.click();

                // 等待对话框出现
                const dialog = await Utils.waitForElement(SELECTORS.AISTUDIO.DIALOG_CONTENT);

                // 点击项目输入框
                const projectInput = await Utils.waitForElement(
                    SELECTORS.AISTUDIO.PROJECT_INPUT,
                    CONFIG.TIMEOUTS.ELEMENT_WAIT,
                    dialog
                );
                projectInput.click();
                await Utils.delay(CONFIG.DELAYS.EXTRA_LONG);

                // 等待项目选项出现
                const projectOptions = await Utils.waitForElements(
                    SELECTORS.AISTUDIO.PROJECT_OPTION,
                    1,
                    CONFIG.TIMEOUTS.ELEMENTS_WAIT
                );

                // 提取项目信息
                const allProjectInfo = Array.from(projectOptions).map((option, index) => {
                    let name = `项目 ${index + 1}`;
                    const nameElement = option.querySelector(SELECTORS.AISTUDIO.PROJECT_NAME);
                    if (nameElement?.textContent) {
                        name = nameElement.textContent.trim();
                    }
                    return { name, index };
                });

                await DialogManager.closeDialog();
                return allProjectInfo;

            } catch (error) {
                console.error('获取项目列表失败:', error);
                await DialogManager.closeDialog();
                throw error;
            }
        }

        /**
         * 为单个项目创建API密钥
         * @param {Object} projectInfo - 项目信息
         * @param {Array} allProjectInfo - 所有项目信息
         * @returns {Promise<string|null>} 生成的API密钥或null
         */
        async createApiKeyForProject(projectInfo, allProjectInfo) {
            const { name: projectName, index: originalIndex } = projectInfo;

            try {
                console.log(`正在为项目 "${projectName}" 创建API密钥...`);

                // 点击创建API密钥按钮
                const createButton = await Utils.waitForElement(SELECTORS.AISTUDIO.CREATE_API_BTN);
                createButton.click();
                await Utils.delay(CONFIG.DELAYS.MEDIUM);

                // 等待对话框出现
                const dialog = await Utils.waitForElement(SELECTORS.AISTUDIO.DIALOG_CONTENT);

                // 点击项目输入框
                const projectInput = await Utils.waitForElement(
                    SELECTORS.AISTUDIO.PROJECT_INPUT,
                    CONFIG.TIMEOUTS.ELEMENT_WAIT,
                    dialog
                );
                projectInput.click();
                await Utils.delay(CONFIG.DELAYS.EXTRA_LONG);

                // 获取项目选项并选择目标项目
                const totalProjectCount = allProjectInfo.length;
                const projectOptions = await Utils.waitForElements(
                    SELECTORS.AISTUDIO.PROJECT_OPTION,
                    totalProjectCount,
                    CONFIG.TIMEOUTS.ELEMENTS_WAIT
                );

                // 使用原始索引选择正确的项目
                const targetOption = projectOptions[originalIndex];
                targetOption.click();
                targetOption.dispatchEvent(new Event('change', { bubbles: true }));
                await Utils.delay(CONFIG.DELAYS.MEDIUM);

                // 点击在项目中创建API密钥按钮
                const createInProjectButton = await Utils.waitForElement(
                    SELECTORS.AISTUDIO.CREATE_IN_PROJECT_BTN,
                    CONFIG.TIMEOUTS.SHORT_WAIT,
                    dialog,
                    false
                );
                createInProjectButton.click();

                // 等待API密钥显示
                const keyDisplayElement = await Utils.waitForElement(
                    SELECTORS.AISTUDIO.API_KEY_DISPLAY,
                    CONFIG.TIMEOUTS.DIALOG_WAIT,
                    document,
                    false
                );

                // 提取API密钥
                const apiKey = keyDisplayElement.textContent?.trim() ||
                              keyDisplayElement.value || '';

                if (apiKey) {
                    console.log(`项目 "${projectName}" API密钥创建成功`);
                    return apiKey;
                } else {
                    console.warn(`项目 "${projectName}" API密钥为空`);
                    return null;
                }

            } catch (error) {
                console.error(`项目 "${projectName}" API密钥创建失败:`, error);
                return null;
            } finally {
                await DialogManager.closeDialog();
            }
        }

        /**
         * 执行API密钥创建流程
         * @param {Function} progressCallback - 进度回调函数
         * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
         */
        async run(progressCallback = null) {
            if (!ApiKeyCreator.isValidPage()) {
                ApiKeyCreator.redirectToApiKeyPage();
                return { current: 0, total: 0 };
            }

            console.log('开始API密钥创建流程...');

            // 初始化进度显示
            if (progressCallback) {
                progressCallback(0, 0, '获取项目列表...');
            }

            // 获取现有项目名称
            const existingProjectNames = this.getExistingProjectNames();

            // 获取所有可用项目
            let allProjectInfo;
            try {
                allProjectInfo = await this.getAvailableProjects();
            } catch (error) {
                console.error('获取项目列表失败:', error);
                if (progressCallback) {
                    progressCallback(0, 0, '获取项目列表失败');
                }
                return { current: 0, total: 0 };
            }

            // 过滤出需要创建API密钥的新项目
            const newProjects = allProjectInfo.filter(project =>
                !existingProjectNames.has(project.name)
            );

            console.log(`弹窗中共有 ${allProjectInfo.length} 个项目，过滤后剩余 ${newProjects.length} 个新项目:`,
                       newProjects.map(p => p.name));

            if (newProjects.length === 0) {
                console.log('没有新项目需要创建API密钥，所有项目都已存在');
                if (progressCallback) {
                    progressCallback(0, 0, '无新项目需要处理');
                }
                return { current: 0, total: 0 };
            }

            // 为每个新项目创建API密钥
            for (let i = 0; i < newProjects.length; i++) {
                const project = newProjects[i];

                // 更新进度显示
                if (progressCallback) {
                    progressCallback(i, newProjects.length, `处理项目: ${project.name}`);
                }

                const apiKey = await this.createApiKeyForProject(project, allProjectInfo);

                if (apiKey) {
                    this.projectSummary[project.name] = [apiKey];
                    this.allKeys.push(apiKey);
                }

                console.log(`进度: ${i + 1}/${newProjects.length}`);

                // 更新完成进度
                if (progressCallback) {
                    progressCallback(i + 1, newProjects.length, `已完成 ${i + 1} 个项目`);
                }
            }

            // 复制结果到剪贴板
            if (this.allKeys.length > 0) {
                if (progressCallback) {
                    progressCallback(newProjects.length, newProjects.length, '复制到剪贴板...');
                }

                const keysText = this.allKeys.join('\n');
                const successMessage = `已复制 ${this.allKeys.length} 个 API KEY 到剪贴板`;
                const fallbackMessage = '剪贴板写入被拦截，已在页面弹出所有 KEY，请手动复制';

                ClipboardManager.copyToClipboard(keysText, successMessage, fallbackMessage);
                console.log('API密钥创建流程完成:', this.projectSummary);
            } else {
                console.log('未成功创建任何API密钥');
            }

            return { current: this.allKeys.length, total: newProjects.length };
        }
    }

    /**
     * 运行API密钥创建流程
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
     */
    async function runApiKeyCreation(progressCallback = null) {
        const creator = new ApiKeyCreator();
        return await creator.run(progressCallback);
    }



    // {{ AURA-X: Modify - 重构API KEY提取流程，提高代码组织性和可维护性 }}
    /**
     * API密钥提取管理类
     * 负责从AI Studio中提取现有的API密钥
     */
    class ApiKeyExtractor {
        constructor(extractStrategy = 'latest') {
            this.extractStrategy = extractStrategy;
            this.allKeys = [];
            this.projectKeys = {};
        }

        /**
         * 提取单个项目的API密钥
         * @param {Element} projectRow - 项目行元素
         * @param {number} projectIndex - 项目索引
         * @returns {Promise<void>}
         */
        async extractKeysFromProject(projectRow, projectIndex) {
            // 获取项目名称
            let projectName = `项目 ${projectIndex + 1}`;
            const nameElement = projectRow.querySelector(SELECTORS.AISTUDIO.PROJECT_NAME_CELL);
            if (nameElement?.textContent) {
                projectName = nameElement.textContent.trim();
            }

            this.projectKeys[projectName] = [];

            // 获取项目中的所有API密钥链接
            const keyLinks = projectRow.querySelectorAll(SELECTORS.AISTUDIO.API_KEY_LINK);

            console.log(`正在提取项目 "${projectName}" 的API密钥，共 ${keyLinks.length} 个`);

            for (let linkIndex = 0; linkIndex < keyLinks.length; linkIndex++) {
                try {
                    // 点击API密钥链接显示完整密钥
                    keyLinks[linkIndex].click();
                    await Utils.delay(CONFIG.DELAYS.STANDARD);

                    // 等待API密钥显示元素出现
                    const keyDisplayElement = await Utils.waitForElement(
                        SELECTORS.AISTUDIO.API_KEY_DISPLAY,
                        CONFIG.TIMEOUTS.DIALOG_WAIT
                    );

                    // 提取API密钥文本
                    const apiKey = keyDisplayElement.textContent?.trim() ||
                                  keyDisplayElement.value || '';

                    if (apiKey && !this.allKeys.includes(apiKey)) {
                        this.allKeys.push(apiKey);
                        this.projectKeys[projectName].push(apiKey);
                        console.log(`成功提取项目 "${projectName}" 的第 ${linkIndex + 1} 个密钥`);
                    }

                    // 关闭密钥显示对话框
                    await this.closeKeyRevealDialog();

                    // 如果策略是仅提取最新密钥，提取第一个后跳出循环
                    if (this.extractStrategy === 'latest' && this.projectKeys[projectName].length > 0) {
                        console.log(`${projectName}: 已提取最新密钥，跳过其余密钥`);
                        break;
                    }

                } catch (error) {
                    console.error(`提取项目 "${projectName}" 第 ${linkIndex + 1} 个密钥失败:`, error);
                    try {
                        await this.closeKeyRevealDialog();
                    } catch (closeError) {
                        console.warn('关闭对话框失败:', closeError);
                    }
                }
            }
        }

        /**
         * 关闭API密钥显示对话框
         * @returns {Promise<void>}
         */
        async closeKeyRevealDialog() {
            const closeSelectors = ["button[aria-label='关闭']"];

            for (const selector of closeSelectors) {
                try {
                    const closeButton = await Utils.waitForElement(
                        selector,
                        CONFIG.TIMEOUTS.SHORT_WAIT
                    );
                    closeButton.click();
                    await Utils.delay(CONFIG.DELAYS.LONG + 200);

                    // 检查对话框是否已关闭
                    if (!document.querySelector(SELECTORS.AISTUDIO.API_KEY_DISPLAY)) {
                        return;
                    }
                } catch (error) {
                    // 忽略等待超时错误，尝试下一个选择器
                }
            }

            // 如果所有选择器都失败，尝试点击页面其他区域关闭对话框
            document.body.click();
            await Utils.delay(CONFIG.DELAYS.LONG + 200);
        }

        /**
         * 执行API密钥提取流程
         * @param {Function} progressCallback - 进度回调函数
         * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
         */
        async run(progressCallback = null) {
            console.clear();
            console.log(`执行密钥提取，策略：${this.extractStrategy === 'latest' ? '仅提取最新密钥' : '提取所有密钥'}`);

            // 初始化进度显示
            if (progressCallback) {
                progressCallback(0, 0, '查找项目...');
            }

            // 获取所有项目行
            const projectRows = document.querySelectorAll(SELECTORS.AISTUDIO.PROJECT_ROW);

            if (projectRows.length === 0) {
                console.warn('未找到任何项目行');
                if (progressCallback) {
                    progressCallback(0, 0, '未找到项目');
                }
                alert('未找到任何项目');
                return { current: 0, total: 0 };
            }

            console.log(`找到 ${projectRows.length} 个项目，开始提取API密钥...`);

            // 遍历每个项目提取API密钥
            for (let i = 0; i < projectRows.length; i++) {
                // 更新进度显示
                if (progressCallback) {
                    progressCallback(i, projectRows.length, `提取第${i + 1}个项目...`);
                }

                await this.extractKeysFromProject(projectRows[i], i);

                // 更新完成进度
                if (progressCallback) {
                    progressCallback(i + 1, projectRows.length, `已完成 ${i + 1} 个项目`);
                }
            }

            // 处理提取结果
            if (progressCallback) {
                progressCallback(projectRows.length, projectRows.length, '处理结果...');
            }

            this.handleExtractionResults();

            return { current: this.allKeys.length, total: projectRows.length };
        }

        /**
         * 处理提取结果，复制到剪贴板或显示文本框
         */
        handleExtractionResults() {
            if (this.allKeys.length > 0) {
                const keysText = this.allKeys.join('\n');
                const strategyText = this.extractStrategy === 'latest' ? '（仅最新密钥）' : '（所有密钥）';
                const successMessage = `已复制 ${this.allKeys.length} 个 API KEY 到剪贴板 ${strategyText}`;
                const fallbackMessage = `剪贴板写入被拦截，已在页面弹出所有 KEY ${strategyText}，请手动复制`;

                ClipboardManager.copyToClipboard(keysText, successMessage, fallbackMessage);

                console.log('API密钥提取完成:', this.projectKeys);
                console.log(`总共提取了 ${this.allKeys.length} 个API密钥`);
            } else {
                console.log('未找到任何API密钥');
                alert('未找到任何 API KEY');
            }
        }
    }

    /**
     * 运行API密钥提取流程
     * @param {string} extractStrategy - 提取策略 ('latest' 或 'all')
     * @param {Function} progressCallback - 进度回调函数
     * @returns {Promise<Object>} 返回结果对象，包含当前进度和总数
     */
    async function runExtractKeys(extractStrategy = 'latest', progressCallback = null) {
        const extractor = new ApiKeyExtractor(extractStrategy);
        return await extractor.run(progressCallback);
    }

    // {{ AURA-X: Add - 添加UI管理类，统一界面操作 }}
    /**
     * 用户界面管理类
     * 负责创建和管理浮动按钮界面
     */
    class UIManager {
        constructor() {
            this.isExtractMenuExpanded = false;
            this.buttonsContainer = null;

            // {{ AURA-X: Add - 添加拖拽功能相关属性 }}
            this.isDragging = false;
            this.dragOffset = { x: 0, y: 0 };
            this.dragStartPos = { x: 0, y: 0 };
            this.animationFrameId = null;
            this.savedPosition = null;
            // {{ AURA-X: Add - 添加性能优化相关属性 }}
            this.lastMoveTime = 0;
            this.currentPosition = { x: 0, y: 0 };
            this.targetPosition = { x: 0, y: 0 };
        }

        /**
         * 检查按钮容器是否已存在
         * @returns {boolean} 是否已存在
         */
        isButtonsContainerExists() {
            return document.getElementById('ai-floating-buttons') !== null;
        }

        // {{ AURA-X: Add - 添加拖拽功能相关方法 }}
        /**
         * 加载保存的位置信息
         * @returns {Object|null} 位置信息对象或null
         */
        loadSavedPosition() {
            try {
                const savedPos = GM_getValue(CONFIG.STORAGE_KEYS.BUTTON_POSITION, null);
                return savedPos ? JSON.parse(savedPos) : null;
            } catch (error) {
                console.warn('加载按钮位置失败:', error);
                return null;
            }
        }

        /**
         * 保存当前位置信息
         * @param {number} top - 顶部位置
         * @param {number} left - 左侧位置
         */
        saveCurrentPosition(top, left) {
            try {
                const position = { top, left, timestamp: Date.now() };
                GM_setValue(CONFIG.STORAGE_KEYS.BUTTON_POSITION, JSON.stringify(position));
                this.savedPosition = position;
            } catch (error) {
                console.warn('保存按钮位置失败:', error);
            }
        }

        /**
         * 将位置限制在视口范围内
         * @param {number} x - X坐标
         * @param {number} y - Y坐标
         * @param {number} width - 元素宽度
         * @param {number} height - 元素高度
         * @returns {Object} 限制后的位置
         */
        constrainToViewport(x, y, width, height) {
            const padding = CONFIG.DRAG.BOUNDARY_PADDING;
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // 限制X坐标
            const minX = padding;
            const maxX = viewportWidth - width - padding;
            const constrainedX = Math.max(minX, Math.min(maxX, x));

            // 限制Y坐标
            const minY = padding;
            const maxY = viewportHeight - height - padding;
            const constrainedY = Math.max(minY, Math.min(maxY, y));

            return { x: constrainedX, y: constrainedY };
        }

        /**
         * 获取鼠标或触摸位置
         * @param {Event} event - 事件对象
         * @returns {Object} 位置坐标
         */
        getEventPosition(event) {
            if (event.touches && event.touches.length > 0) {
                return { x: event.touches[0].clientX, y: event.touches[0].clientY };
            }
            return { x: event.clientX, y: event.clientY };
        }

        /**
         * 处理拖拽开始
         * @param {Event} event - 事件对象
         */
        handleDragStart(event) {
            // {{ AURA-X: Modify - 现在使用专用拖拽手柄，简化逻辑 }}
            // 防止默认行为和事件冒泡
            event.preventDefault();
            event.stopPropagation();

            if (this.isDragging) return;

            const pos = this.getEventPosition(event);
            const rect = this.buttonsContainer.getBoundingClientRect();

            this.isDragging = true;
            this.dragStartPos = { x: pos.x, y: pos.y };
            this.dragOffset = {
                x: pos.x - rect.left,
                y: pos.y - rect.top
            };

            // {{ AURA-X: Add - 初始化位置记录 }}
            this.currentPosition = { x: rect.left, y: rect.top };
            this.targetPosition = { x: rect.left, y: rect.top };
            this.lastMoveTime = performance.now();

            // 添加拖拽状态样式
            this.buttonsContainer.style.opacity = '0.8';
            this.buttonsContainer.style.transform = 'scale(1.05)';
            this.buttonsContainer.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)';
            this.buttonsContainer.style.transition = 'none';

            // 更新拖拽手柄样式
            const dragHandle = this.buttonsContainer.querySelector('.drag-handle');
            if (dragHandle) {
                dragHandle.style.cursor = 'grabbing';
                dragHandle.style.opacity = '1';
            }

            // 禁用按钮功能
            this.disableButtons(true);

            // 添加全局事件监听器
            document.addEventListener('mousemove', this.handleDragMove.bind(this));
            document.addEventListener('mouseup', this.handleDragEnd.bind(this));
            document.addEventListener('touchmove', this.handleDragMove.bind(this), { passive: false });
            document.addEventListener('touchend', this.handleDragEnd.bind(this));

            console.log('开始拖拽');
        }

        /**
         * 处理拖拽移动
         * @param {Event} event - 事件对象
         */
        handleDragMove(event) {
            if (!this.isDragging) return;

            event.preventDefault();
            event.stopPropagation();

            // {{ AURA-X: Modify - 优化拖拽性能，使用节流和transform }}
            const now = performance.now();

            // 节流处理，提高性能
            if (now - this.lastMoveTime < CONFIG.DRAG.THROTTLE_DELAY) {
                return;
            }
            this.lastMoveTime = now;

            // 取消之前的动画帧
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }

            // 使用requestAnimationFrame优化性能
            this.animationFrameId = requestAnimationFrame(() => {
                const pos = this.getEventPosition(event);
                const rect = this.buttonsContainer.getBoundingClientRect();

                // 计算新位置
                const newX = pos.x - this.dragOffset.x;
                const newY = pos.y - this.dragOffset.y;

                // 限制在视口范围内
                const constrained = this.constrainToViewport(newX, newY, rect.width, rect.height);

                // 更新目标位置
                this.targetPosition = { x: constrained.x, y: constrained.y };

                // {{ AURA-X: Modify - 修复transform位置计算问题，统一使用left/top }}
                // 统一使用left/top方式，避免transform位置计算复杂性
                this.buttonsContainer.style.left = `${constrained.x}px`;
                this.buttonsContainer.style.top = `${constrained.y}px`;
                this.buttonsContainer.style.right = 'auto';
                this.buttonsContainer.style.bottom = 'auto';

                // 更新当前位置
                this.currentPosition = { x: constrained.x, y: constrained.y };
            });
        }

        /**
         * 处理拖拽结束
         * @param {Event} event - 事件对象
         */
        handleDragEnd(event) {
            if (!this.isDragging) return;

            event.preventDefault();
            event.stopPropagation();

            // 取消动画帧
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
                this.animationFrameId = null;
            }

            // 检查是否真正进行了拖拽（移动距离超过阈值）
            const pos = this.getEventPosition(event);
            const dragDistance = Math.sqrt(
                Math.pow(pos.x - this.dragStartPos.x, 2) +
                Math.pow(pos.y - this.dragStartPos.y, 2)
            );

            const wasDragged = dragDistance > CONFIG.DRAG.DRAG_THRESHOLD;

            // 恢复样式
            this.buttonsContainer.style.transition = `all ${CONFIG.DRAG.ANIMATION_DURATION}ms ease`;
            this.buttonsContainer.style.opacity = '1';
            this.buttonsContainer.style.transform = 'scale(1)';
            this.buttonsContainer.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';

            // 恢复拖拽手柄样式
            const dragHandle = this.buttonsContainer.querySelector('.drag-handle');
            if (dragHandle) {
                dragHandle.style.cursor = 'grab';
                dragHandle.style.opacity = '0.6';
            }

            // {{ AURA-X: Modify - 简化位置保存逻辑，修复位置跳转问题 }}
            // 如果进行了拖拽，保存新位置
            if (wasDragged) {
                // 直接从当前记录的位置获取，避免getBoundingClientRect的问题
                const finalX = this.currentPosition ? this.currentPosition.x : 0;
                const finalY = this.currentPosition ? this.currentPosition.y : 0;

                this.saveCurrentPosition(finalY, finalX);
                console.log('拖拽完成，位置已保存:', { top: finalY, left: finalX });
            }

            // 移除全局事件监听器
            document.removeEventListener('mousemove', this.handleDragMove.bind(this));
            document.removeEventListener('mouseup', this.handleDragEnd.bind(this));
            document.removeEventListener('touchmove', this.handleDragMove.bind(this));
            document.removeEventListener('touchend', this.handleDragEnd.bind(this));

            // 延迟恢复按钮功能，避免误触
            setTimeout(() => {
                this.isDragging = false;
                this.disableButtons(false);
            }, wasDragged ? CONFIG.DRAG.ANIMATION_DURATION : 0);
        }

        /**
         * 禁用或启用按钮功能
         * @param {boolean} disable - 是否禁用
         */
        disableButtons(disable) {
            if (!this.buttonsContainer) return;

            const buttons = this.buttonsContainer.querySelectorAll('button');
            buttons.forEach(button => {
                if (disable) {
                    button.style.pointerEvents = 'none';
                    button.setAttribute('data-drag-disabled', 'true');
                } else {
                    button.style.pointerEvents = 'auto';
                    button.removeAttribute('data-drag-disabled');
                }
            });
        }

        /**
         * 处理双击重置位置
         * @param {Event} event - 事件对象
         */
        handleDoubleClick(event) {
            event.preventDefault();
            event.stopPropagation();

            console.log('双击重置位置');

            // 重置到默认位置
            this.buttonsContainer.style.transition = `all ${CONFIG.DRAG.ANIMATION_DURATION * 2}ms ease`;
            this.buttonsContainer.style.top = `${CONFIG.DRAG.DEFAULT_POSITION.top}px`;
            this.buttonsContainer.style.right = `${CONFIG.DRAG.DEFAULT_POSITION.right}px`;
            this.buttonsContainer.style.left = 'auto';
            this.buttonsContainer.style.bottom = 'auto';

            // 清除保存的位置
            try {
                GM_setValue(CONFIG.STORAGE_KEYS.BUTTON_POSITION, '');
                this.savedPosition = null;
                console.log('位置已重置到默认位置');
            } catch (error) {
                console.warn('清除保存位置失败:', error);
            }
        }

        /**
         * 创建基础按钮样式
         * @param {HTMLElement} button - 按钮元素
         * @param {Object} additionalStyles - 额外样式
         */
        applyButtonStyles(button, additionalStyles = {}) {
            const baseStyles = {
                padding: '5px 10px',
                fontSize: '14px',
                cursor: 'pointer',
                border: '1px solid #ccc',
                borderRadius: '3px',
                backgroundColor: '#f9f9f9',
                color: '#333',
                transition: 'all 0.2s ease'
            };

            Object.assign(button.style, baseStyles, additionalStyles);
        }

        /**
         * 创建主要功能按钮
         * @returns {Object} 包含所有按钮的对象
         */
        createMainButtons() {
            const createProjectBtn = document.createElement('button');
            const createApiKeyBtn = document.createElement('button');
            const extractApiKeyBtn = document.createElement('button');

            createProjectBtn.textContent = '创建项目';
            createApiKeyBtn.textContent = '创建API密钥';
            extractApiKeyBtn.textContent = '提取API密钥 ▼';

            // 应用按钮样式
            [createProjectBtn, createApiKeyBtn, extractApiKeyBtn].forEach(btn => {
                this.applyButtonStyles(btn);
            });

            return {
                createProject: createProjectBtn,
                createApiKey: createApiKeyBtn,
                extractApiKey: extractApiKeyBtn
            };
        }

        /**
         * 创建提取子菜单按钮
         * @returns {Object} 包含子菜单相关元素的对象
         */
        createExtractSubMenu() {
            const subContainer = document.createElement('div');
            const extractLatestBtn = document.createElement('button');
            const extractAllBtn = document.createElement('button');

            extractLatestBtn.textContent = '仅提取项目最新密钥';
            extractAllBtn.textContent = '提取项目所有密钥';

            // 子按钮样式
            [extractLatestBtn, extractAllBtn].forEach(btn => {
                this.applyButtonStyles(btn, {
                    fontSize: '12px',
                    backgroundColor: '#f0f0f0',
                    marginTop: '2px'
                });
            });

            // 子容器样式（默认隐藏，左侧展开，垂直排列）
            Object.assign(subContainer.style, {
                display: 'none',
                flexDirection: 'column',
                position: 'absolute',
                top: '0',
                right: '100%',
                marginRight: '5px',
                minWidth: '140px',
                backgroundColor: 'rgba(255,255,255,0.95)',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                zIndex: '10001',
                gap: '2px',
                padding: '3px'
            });

            subContainer.appendChild(extractLatestBtn);
            subContainer.appendChild(extractAllBtn);

            return {
                container: subContainer,
                extractLatest: extractLatestBtn,
                extractAll: extractAllBtn
            };
        }

        /**
         * 创建按钮容器
         * @returns {HTMLElement} 按钮容器元素
         */
        createButtonsContainer() {
            const container = document.createElement('div');
            container.id = 'ai-floating-buttons';

            // {{ AURA-X: Modify - 添加拖拽功能相关样式和位置处理 }}
            // 加载保存的位置
            const savedPos = this.loadSavedPosition();

            const baseStyles = {
                position: 'fixed',
                zIndex: '9999',
                display: 'flex',
                flexDirection: 'column',
                gap: '5px',
                background: 'rgba(255,255,255,0.9)',
                padding: '5px',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                backdropFilter: 'blur(5px)',
                // 拖拽相关样式
                cursor: 'grab',
                userSelect: 'none',
                touchAction: 'none',
                transition: 'all 200ms ease'
            };

            // 设置位置
            if (savedPos && savedPos.top !== undefined && savedPos.left !== undefined) {
                // 使用保存的位置
                baseStyles.top = `${savedPos.top}px`;
                baseStyles.left = `${savedPos.left}px`;
                baseStyles.right = 'auto';
                baseStyles.bottom = 'auto';
                console.log('恢复保存的按钮位置:', savedPos);
            } else {
                // 使用默认位置
                baseStyles.top = `${CONFIG.DRAG.DEFAULT_POSITION.top}px`;
                baseStyles.right = `${CONFIG.DRAG.DEFAULT_POSITION.right}px`;
                baseStyles.left = 'auto';
                baseStyles.bottom = 'auto';
            }

            Object.assign(container.style, baseStyles);

            // 初始化拖拽功能
            this.initializeDragFunctionality(container);

            return container;
        }

        /**
         * 创建拖拽手柄区域
         * @param {HTMLElement} container - 按钮容器元素
         * @returns {HTMLElement} 拖拽手柄元素
         */
        createDragHandle(container) {
            const dragHandle = document.createElement('div');
            dragHandle.className = 'drag-handle';

            Object.assign(dragHandle.style, {
                width: '100%',
                height: '8px',
                background: 'linear-gradient(90deg, #ccc 0%, #999 50%, #ccc 100%)',
                borderRadius: '4px 4px 0 0',
                cursor: 'grab',
                position: 'relative',
                marginBottom: '3px',
                opacity: '0.6',
                transition: 'opacity 0.2s ease'
            });

            // 添加拖拽指示器
            const indicator = document.createElement('div');
            Object.assign(indicator.style, {
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                width: '20px',
                height: '2px',
                background: '#666',
                borderRadius: '1px'
            });

            const indicator2 = indicator.cloneNode();
            indicator2.style.marginTop = '3px';

            dragHandle.appendChild(indicator);
            dragHandle.appendChild(indicator2);

            // 悬停效果
            dragHandle.addEventListener('mouseenter', () => {
                dragHandle.style.opacity = '1';
                dragHandle.style.cursor = 'grab';
            });

            dragHandle.addEventListener('mouseleave', () => {
                if (!this.isDragging) {
                    dragHandle.style.opacity = '0.6';
                }
            });

            return dragHandle;
        }

        /**
         * 初始化拖拽功能
         * @param {HTMLElement} container - 按钮容器元素
         */
        initializeDragFunctionality(container) {
            // {{ AURA-X: Modify - 添加专用拖拽手柄，改善用户体验 }}
            // 创建拖拽手柄
            const dragHandle = this.createDragHandle(container);
            container.insertBefore(dragHandle, container.firstChild);

            // 只在拖拽手柄上监听拖拽事件
            dragHandle.addEventListener('mousedown', this.handleDragStart.bind(this));
            dragHandle.addEventListener('touchstart', this.handleDragStart.bind(this), { passive: false });

            // 容器的双击重置位置（保留原功能）
            container.addEventListener('dblclick', this.handleDoubleClick.bind(this));

            // 防止拖拽时选中文本
            container.addEventListener('selectstart', (e) => e.preventDefault());
            container.addEventListener('dragstart', (e) => e.preventDefault());

            // 容器悬停效果（轻微）
            container.addEventListener('mouseenter', () => {
                if (!this.isDragging) {
                    container.style.transform = 'scale(1.01)';
                    container.style.boxShadow = '0 4px 8px rgba(0,0,0,0.25)';
                }
            });

            container.addEventListener('mouseleave', () => {
                if (!this.isDragging) {
                    container.style.transform = 'scale(1)';
                    container.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
                }
            });

            // 窗口大小改变时重新约束位置
            window.addEventListener('resize', () => {
                if (container && !this.isDragging) {
                    const rect = container.getBoundingClientRect();
                    const constrained = this.constrainToViewport(rect.left, rect.top, rect.width, rect.height);

                    if (constrained.x !== rect.left || constrained.y !== rect.top) {
                        container.style.left = `${constrained.x}px`;
                        container.style.top = `${constrained.y}px`;
                        container.style.right = 'auto';
                        container.style.bottom = 'auto';
                        this.saveCurrentPosition(constrained.y, constrained.x);
                    }
                }
            });

            console.log('拖拽功能已初始化');
        }

        // {{ AURA-X: Add - 添加进度显示相关方法 }}
        /**
         * 格式化按钮文字，包含进度信息
         * @param {string} baseText - 基础文字
         * @param {number} current - 当前进度
         * @param {number} total - 总数
         * @param {string} status - 状态文字
         * @returns {string} 格式化后的文字
         */
        formatButtonTextWithProgress(baseText, current = 0, total = 0, status = '') {
            let text = baseText;

            // 添加进度信息
            if (total > 0) {
                text += ` (${current}/${total})`;
            }

            // 添加状态信息
            if (status) {
                text += ` - ${status}`;
            }

            return text;
        }

        /**
         * 更新按钮进度显示
         * @param {HTMLElement} button - 按钮元素
         * @param {string} baseText - 基础文字
         * @param {number} current - 当前进度
         * @param {number} total - 总数
         * @param {string} status - 状态文字
         */
        updateButtonProgress(button, baseText, current = 0, total = 0, status = '运行中...') {
            if (!button) return;

            const formattedText = this.formatButtonTextWithProgress(baseText, current, total, status);
            button.textContent = formattedText;
        }

        /**
         * 创建进度回调函数
         * @param {HTMLElement} button - 按钮元素
         * @param {string} baseText - 基础文字
         * @returns {Function} 进度回调函数
         */
        createProgressCallback(button, baseText) {
            return (current, total, status = '运行中...') => {
                this.updateButtonProgress(button, baseText, current, total, status);
            };
        }

        /**
         * 处理按钮点击事件的通用逻辑（支持进度显示）
         * @param {HTMLElement} button - 按钮元素
         * @param {Function} action - 要执行的异步操作
         * @param {string} originalText - 按钮原始文本
         * @param {string} runningText - 运行时显示的文本
         * @param {boolean} isRedirect - 是否为页面跳转操作
         * @param {boolean} supportProgress - 是否支持进度显示
         */
        async handleButtonClick(button, action, originalText, runningText = '运行中...', isRedirect = false, supportProgress = false) {
            if (button.disabled) return;

            button.disabled = true;

            // 如果不支持进度显示，使用原有逻辑
            if (!supportProgress) {
                button.textContent = runningText;
            }

            try {
                let result;

                if (supportProgress) {
                    // 创建进度回调函数
                    const progressCallback = this.createProgressCallback(button, originalText);
                    result = await action(progressCallback);
                } else {
                    result = await action();
                }

                // {{ AURA-X: Modify - 根据操作类型显示不同的完成状态 }}
                if (isRedirect) {
                    button.textContent = this.formatButtonTextWithProgress(originalText, 0, 0, '跳转中...');
                    // 跳转操作不需要恢复按钮状态，因为页面会刷新
                    return;
                } else if (result === 'redirect') {
                    // 如果操作返回redirect标识，表示进行了页面跳转
                    button.textContent = this.formatButtonTextWithProgress(originalText, 0, 0, '跳转中...');
                    return;
                } else if (supportProgress && result && typeof result === 'object' && result.current !== undefined && result.total !== undefined) {
                    // 显示最终进度
                    button.textContent = this.formatButtonTextWithProgress(originalText, result.current, result.total, '完成');
                } else {
                    button.textContent = supportProgress ? this.formatButtonTextWithProgress(originalText, 0, 0, '完成') : '完成';
                }
            } catch (error) {
                console.error('操作失败:', error);
                button.textContent = supportProgress ? this.formatButtonTextWithProgress(originalText, 0, 0, '错误') : '错误';
            }

            // 3秒后恢复按钮状态（跳转操作除外）
            setTimeout(() => {
                button.disabled = false;
                button.textContent = originalText;
            }, 3000);
        }

        /**
         * 绑定按钮事件
         * @param {Object} buttons - 按钮对象
         * @param {Object} subMenu - 子菜单对象
         */
        bindButtonEvents(buttons, subMenu) {
            // {{ AURA-X: Modify - 启用进度显示功能的创建项目按钮事件 }}
            // 创建项目按钮事件
            buttons.createProject.onclick = () => {
                this.handleButtonClick(
                    buttons.createProject,
                    async (progressCallback) => {
                        if (!ProjectCreator.isValidPage()) {
                            ProjectCreator.redirectToConsole();
                            return 'redirect'; // 返回跳转标识
                        }
                        return await runProjectCreation(progressCallback);
                    },
                    '创建项目',
                    '运行中...',
                    false,
                    true // 启用进度显示
                );
            };

            // {{ AURA-X: Modify - 启用进度显示功能的创建API密钥按钮事件 }}
            // 创建API密钥按钮事件
            buttons.createApiKey.onclick = () => {
                this.handleButtonClick(
                    buttons.createApiKey,
                    async (progressCallback) => {
                        if (!ApiKeyCreator.isValidPage()) {
                            ApiKeyCreator.redirectToApiKeyPage();
                            return 'redirect'; // 返回跳转标识
                        }
                        return await runApiKeyCreation(progressCallback);
                    },
                    '创建API密钥',
                    '运行中...',
                    false,
                    true // 启用进度显示
                );
            };

            // 提取API密钥主按钮事件（展开/收起子菜单）
            buttons.extractApiKey.onclick = () => {
                this.isExtractMenuExpanded = !this.isExtractMenuExpanded;
                if (this.isExtractMenuExpanded) {
                    subMenu.container.style.display = 'flex';
                    buttons.extractApiKey.textContent = '提取API密钥 ▲';
                } else {
                    subMenu.container.style.display = 'none';
                    buttons.extractApiKey.textContent = '提取API密钥 ▼';
                }
            };

            // {{ AURA-X: Modify - 启用进度显示功能的子按钮事件处理 }}
            // 子按钮事件处理
            const executeExtraction = async (strategy, btnElement, strategyText) => {
                // 隐藏子菜单
                subMenu.container.style.display = 'none';
                buttons.extractApiKey.textContent = '提取API密钥 ▼';
                this.isExtractMenuExpanded = false;

                // 执行提取
                await this.handleButtonClick(
                    btnElement,
                    async (progressCallback) => {
                        if (!/aistudio\.google\.com/.test(location.host)) {
                            location.href = "https://aistudio.google.com/apikey";
                            return 'redirect'; // 返回跳转标识
                        }
                        return await runExtractKeys(strategy, progressCallback);
                    },
                    strategyText,
                    '运行中...',
                    false,
                    true // 启用进度显示
                );
            };

            subMenu.extractLatest.onclick = () =>
                executeExtraction('latest', subMenu.extractLatest, '仅提取最新密钥');

            subMenu.extractAll.onclick = () =>
                executeExtraction('all', subMenu.extractAll, '提取所有密钥');
        }

        /**
         * 初始化按钮界面
         */
        initializeButtons() {
            if (this.isButtonsContainerExists()) {
                return;
            }

            // 创建容器和按钮
            this.buttonsContainer = this.createButtonsContainer();
            const mainButtons = this.createMainButtons();
            const subMenu = this.createExtractSubMenu();

            // 创建提取按钮容器
            const extractContainer = document.createElement('div');
            Object.assign(extractContainer.style, {
                position: 'relative',
                display: 'flex',
                flexDirection: 'column'
            });

            // 组装提取按钮容器
            extractContainer.appendChild(mainButtons.extractApiKey);
            extractContainer.appendChild(subMenu.container);

            // 组装主容器
            this.buttonsContainer.appendChild(mainButtons.createProject);
            this.buttonsContainer.appendChild(mainButtons.createApiKey);
            this.buttonsContainer.appendChild(extractContainer);

            // 绑定事件
            this.bindButtonEvents(mainButtons, subMenu);

            // 添加到页面
            document.body.appendChild(this.buttonsContainer);
        }
    }

    // 创建UI管理器实例
    const uiManager = new UIManager();

    /**
     * 初始化按钮界面的入口函数
     */
    function initButtons() {
        uiManager.initializeButtons();
    }

    // {{ AURA-X: Add - 添加脚本初始化管理类，统一初始化逻辑 }}
    /**
     * 脚本初始化管理类
     * 负责脚本的启动和事件监听
     */
    class ScriptInitializer {
        constructor() {
            this.isInitialized = false;
            this.mutationObserver = null;
            this.initializationTimer = null;
        }

        /**
         * 设置SPA路由监听
         * 监听单页应用的路由变化
         */
        setupSPARouteListener() {
            const wrapHistoryMethod = (methodName) => {
                const originalMethod = history[methodName];
                history[methodName] = function() {
                    const result = originalMethod.apply(this, arguments);
                    window.dispatchEvent(new Event(methodName));
                    window.dispatchEvent(new Event('locationchange'));
                    return result;
                };
            };

            // 包装pushState和replaceState方法
            wrapHistoryMethod('pushState');
            wrapHistoryMethod('replaceState');

            // 监听popstate事件
            window.addEventListener('popstate', () => {
                window.dispatchEvent(new Event('locationchange'));
            });

            // 监听自定义的locationchange事件
            window.addEventListener('locationchange', () => {
                this.initializeUI();
            });
        }

        /**
         * 设置DOM变化监听
         * 监听DOM结构变化以重新初始化UI
         */
        setupDOMObserver() {
            if (this.mutationObserver) {
                this.mutationObserver.disconnect();
            }

            this.mutationObserver = new MutationObserver((mutations) => {
                // 检查是否有重要的DOM变化
                const hasSignificantChange = mutations.some(mutation =>
                    mutation.type === 'childList' &&
                    (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0)
                );

                if (hasSignificantChange) {
                    this.initializeUI();
                }
            });

            this.mutationObserver.observe(document, {
                childList: true,
                subtree: true
            });
        }

        /**
         * 初始化UI界面
         * 使用防抖机制避免频繁初始化
         */
        initializeUI() {
            // 清除之前的定时器
            if (this.initializationTimer) {
                clearTimeout(this.initializationTimer);
            }

            // 设置新的定时器，延迟执行初始化
            this.initializationTimer = setTimeout(() => {
                try {
                    initButtons();
                } catch (error) {
                    console.error('UI初始化失败:', error);
                }
            }, 100); // 100ms防抖延迟
        }

        /**
         * 启动脚本初始化
         */
        start() {
            if (this.isInitialized) {
                return;
            }

            console.log('AI Studio 多功能脚本正在初始化...');

            // 设置各种事件监听
            this.setupSPARouteListener();
            this.setupDOMObserver();

            // 监听页面加载事件
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeUI());
            }

            window.addEventListener('load', () => this.initializeUI());

            // 延迟初始化，确保页面完全加载
            setTimeout(() => {
                this.initializeUI();
            }, 3000);

            // 设置定期检查（降低频率，避免性能问题）
            setInterval(() => {
                this.initializeUI();
            }, 5000); // 从1秒改为5秒

            this.isInitialized = true;
            console.log('AI Studio 多功能脚本初始化完成');
        }

        /**
         * 停止脚本并清理资源
         */
        stop() {
            if (this.mutationObserver) {
                this.mutationObserver.disconnect();
                this.mutationObserver = null;
            }

            if (this.initializationTimer) {
                clearTimeout(this.initializationTimer);
                this.initializationTimer = null;
            }

            this.isInitialized = false;
            console.log('AI Studio 多功能脚本已停止');
        }
    }

    // {{ AURA-X: Modify - 使用新的初始化管理器启动脚本 }}
    // 创建并启动脚本初始化器
    const scriptInitializer = new ScriptInitializer();
    scriptInitializer.start();

    // 在页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        scriptInitializer.stop();
    });

})();