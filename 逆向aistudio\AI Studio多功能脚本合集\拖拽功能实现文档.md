# AI Studio 多功能脚本 - 拖拽功能实现文档

## 📋 功能概述

本文档详细记录了为AI Studio多功能脚本浮动按钮菜单添加拖拽功能的完整实现过程。该功能允许用户将按钮菜单拖拽到页面的任意位置，并自动保存位置信息。

## 🎯 功能特性

### 核心功能
- ✅ **完整拖拽支持**: 支持鼠标和触摸设备拖拽
- ✅ **位置记忆**: 自动保存和恢复用户设置的位置
- ✅ **边界检测**: 确保按钮始终在可视区域内
- ✅ **专用拖拽手柄**: 独立的拖拽区域，不影响按钮功能

### 用户体验优化
- ✅ **视觉反馈**: 拖拽时透明度、缩放和阴影效果
- ✅ **防误触**: 拖拽时临时禁用按钮功能
- ✅ **双击重置**: 双击恢复到默认位置
- ✅ **响应式适配**: 窗口大小改变时自动调整位置

## 🏗️ 技术架构

### 配置扩展
```javascript
CONFIG.DRAG = {
    ANIMATION_DURATION: 200,        // 动画持续时间（毫秒）
    BOUNDARY_PADDING: 10,           // 边界内边距（像素）
    DRAG_THRESHOLD: 5,              // 拖拽阈值（像素）
    DEFAULT_POSITION: {             // 默认位置
        top: 10,
        right: 10
    }
};

CONFIG.STORAGE_KEYS = {
    BUTTON_POSITION: 'aiStudioFloatingButtonPosition'  // 位置存储键
};
```

### UIManager类扩展

#### 新增属性
```javascript
class UIManager {
    constructor() {
        // 原有属性...
        
        // 拖拽功能相关属性
        this.isDragging = false;           // 拖拽状态标志
        this.dragOffset = { x: 0, y: 0 };  // 拖拽偏移量
        this.dragStartPos = { x: 0, y: 0 }; // 拖拽起始位置
        this.animationFrameId = null;      // 动画帧ID
        this.savedPosition = null;         // 保存的位置信息
    }
}
```

#### 核心方法

##### 1. 位置管理
```javascript
// 加载保存的位置
loadSavedPosition() {
    try {
        const savedPos = GM_getValue(CONFIG.STORAGE_KEYS.BUTTON_POSITION, null);
        return savedPos ? JSON.parse(savedPos) : null;
    } catch (error) {
        console.warn('加载按钮位置失败:', error);
        return null;
    }
}

// 保存当前位置
saveCurrentPosition(top, left) {
    try {
        const position = { top, left, timestamp: Date.now() };
        GM_setValue(CONFIG.STORAGE_KEYS.BUTTON_POSITION, JSON.stringify(position));
        this.savedPosition = position;
    } catch (error) {
        console.warn('保存按钮位置失败:', error);
    }
}
```

##### 2. 边界检测
```javascript
constrainToViewport(x, y, width, height) {
    const padding = CONFIG.DRAG.BOUNDARY_PADDING;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 限制X坐标
    const minX = padding;
    const maxX = viewportWidth - width - padding;
    const constrainedX = Math.max(minX, Math.min(maxX, x));

    // 限制Y坐标
    const minY = padding;
    const maxY = viewportHeight - height - padding;
    const constrainedY = Math.max(minY, Math.min(maxY, y));

    return { x: constrainedX, y: constrainedY };
}
```

##### 3. 拖拽手柄
```javascript
createDragHandle(container) {
    const dragHandle = document.createElement('div');
    dragHandle.className = 'drag-handle';
    
    Object.assign(dragHandle.style, {
        width: '100%',
        height: '8px',
        background: 'linear-gradient(90deg, #ccc 0%, #999 50%, #ccc 100%)',
        borderRadius: '4px 4px 0 0',
        cursor: 'grab',
        position: 'relative',
        marginBottom: '3px',
        opacity: '0.6',
        transition: 'opacity 0.2s ease'
    });

    // 添加拖拽指示器（两条横线）
    const indicator = document.createElement('div');
    // ... 样式设置
    
    return dragHandle;
}
```

## 🎨 用户界面设计

### 拖拽手柄设计
- **位置**: 按钮容器顶部
- **高度**: 8px
- **样式**: 渐变背景 + 双横线指示器
- **交互**: 悬停时高亮显示

### 视觉反馈
```javascript
// 拖拽开始时的样式变化
this.buttonsContainer.style.opacity = '0.8';           // 透明度降低
this.buttonsContainer.style.transform = 'scale(1.05)'; // 轻微放大
this.buttonsContainer.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)'; // 增强阴影
```

### 状态指示
- **正常状态**: 透明度1.0，正常阴影
- **悬停状态**: 轻微放大(1.01)，增强阴影
- **拖拽状态**: 透明度0.8，放大(1.05)，强阴影

## 🔧 事件处理机制

### 事件流程
1. **开始拖拽** (`mousedown` / `touchstart`)
   - 记录起始位置和偏移量
   - 应用拖拽样式
   - 禁用按钮功能
   - 添加全局事件监听器

2. **拖拽移动** (`mousemove` / `touchmove`)
   - 使用 `requestAnimationFrame` 优化性能
   - 计算新位置并应用边界检测
   - 实时更新容器位置

3. **结束拖拽** (`mouseup` / `touchend`)
   - 检查拖拽距离是否超过阈值
   - 恢复正常样式
   - 保存新位置（如果有效拖拽）
   - 移除全局事件监听器
   - 延迟恢复按钮功能

### 性能优化
```javascript
// 使用 requestAnimationFrame 优化拖拽移动
this.animationFrameId = requestAnimationFrame(() => {
    const pos = this.getEventPosition(event);
    const rect = this.buttonsContainer.getBoundingClientRect();
    
    // 计算新位置
    const newX = pos.x - this.dragOffset.x;
    const newY = pos.y - this.dragOffset.y;
    
    // 限制在视口范围内
    const constrained = this.constrainToViewport(newX, newY, rect.width, rect.height);
    
    // 更新位置
    this.buttonsContainer.style.left = `${constrained.x}px`;
    this.buttonsContainer.style.top = `${constrained.y}px`;
});
```

## 📱 多设备支持

### 桌面端支持
- **鼠标事件**: `mousedown`, `mousemove`, `mouseup`
- **拖拽手柄**: 8px高度的专用拖拽区域
- **双击重置**: 双击容器重置到默认位置

### 移动端支持
- **触摸事件**: `touchstart`, `touchmove`, `touchend`
- **触摸优化**: `{ passive: false }` 防止滚动冲突
- **触摸反馈**: 与桌面端相同的视觉反馈

### 响应式处理
```javascript
// 窗口大小改变时重新约束位置
window.addEventListener('resize', () => {
    if (container && !this.isDragging) {
        const rect = container.getBoundingClientRect();
        const constrained = this.constrainToViewport(rect.left, rect.top, rect.width, rect.height);
        
        if (constrained.x !== rect.left || constrained.y !== rect.top) {
            container.style.left = `${constrained.x}px`;
            container.style.top = `${constrained.y}px`;
            this.saveCurrentPosition(constrained.y, constrained.x);
        }
    }
});
```

## 💾 数据存储

### 存储格式
```javascript
{
    top: 100,                    // 顶部位置（像素）
    left: 200,                   // 左侧位置（像素）
    timestamp: 1703123456789     // 保存时间戳
}
```

### 存储机制
- **保存时机**: 拖拽结束且移动距离超过阈值时
- **存储方式**: 使用 `GM_setValue` 持久化存储
- **恢复时机**: 容器创建时自动加载
- **重置功能**: 双击清除存储并恢复默认位置

## 🔒 兼容性保证

### 与现有功能的兼容性
- ✅ **进度显示功能**: 完全兼容，不影响进度更新
- ✅ **按钮功能**: 拖拽时临时禁用，结束后恢复
- ✅ **子菜单展开**: 不影响提取按钮的子菜单功能
- ✅ **样式系统**: 与现有样式系统无冲突

### 错误处理
- **存储失败**: 捕获异常，不影响拖拽功能
- **位置越界**: 自动约束到可视区域内
- **事件冲突**: 正确的事件处理顺序和清理

## 🎯 用户操作指南

### 基本操作
1. **拖拽移动**: 在顶部拖拽手柄区域按住拖拽
2. **位置保存**: 拖拽结束后自动保存位置
3. **重置位置**: 双击按钮容器恢复默认位置

### 视觉提示
- **拖拽手柄**: 顶部灰色渐变条，悬停时高亮
- **拖拽状态**: 容器变透明并轻微放大
- **边界限制**: 自动约束在可视区域内

## 📈 性能指标

### 优化措施
- **RAF优化**: 使用 `requestAnimationFrame` 优化拖拽动画
- **事件节流**: 避免频繁的DOM操作
- **内存管理**: 及时清理事件监听器和动画帧
- **样式缓存**: 减少重复的样式计算

### 性能表现
- **拖拽延迟**: < 16ms (60fps)
- **内存占用**: 最小化事件监听器
- **CPU使用**: 拖拽时适度增加，结束后恢复正常

## 🔮 未来扩展

### 可能的增强功能
- **磁性吸附**: 靠近边缘时自动吸附
- **多位置预设**: 保存多个常用位置
- **手势支持**: 更多触摸手势操作
- **主题适配**: 根据页面主题调整拖拽手柄样式

## 📝 总结

拖拽功能的成功实现为AI Studio多功能脚本带来了显著的用户体验提升：

1. **用户自由度**: 用户可以将按钮放置在最舒适的位置
2. **位置记忆**: 无需每次重新调整位置
3. **多设备支持**: 桌面和移动设备都能良好工作
4. **性能优化**: 流畅的拖拽体验，无卡顿现象
5. **完全兼容**: 与现有所有功能完美兼容

该功能的实现展示了现代Web应用中用户界面交互的最佳实践，为用户提供了更加个性化和便捷的使用体验。
